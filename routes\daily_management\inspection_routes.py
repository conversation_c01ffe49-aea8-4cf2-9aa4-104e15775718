"""
检查记录相关功能模块
"""
from .common_imports import *
from datetime import datetime
from flask_wtf import FlaskForm

# 检查模板管理
@daily_management_bp.route('/inspection-templates')
@login_required
def inspection_templates():
    """检查模板管理页面"""
    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area:
        flash('您没有关联到任何学校，无法访问检查模板管理', 'danger')
        return redirect(url_for('main.index'))

    # 获取URL参数
    log_id = request.args.get('log_id', type=int)
    inspection_type = request.args.get('inspection_type')

    return render_template('daily_management/inspection_templates.html',
                          title=f'{user_area.name} - 检查模板管理',
                          school=user_area,
                          log_id=log_id,
                          inspection_type=inspection_type)

@daily_management_bp.route('/simplified-inspection/<int:log_id>', methods=['GET'])
@login_required
def simplified_inspection(log_id):
    """检查记录简化视图

    这是检查记录的主要显示视图，其他视图都已重定向到此视图。

    参数:
        log_id: 日志ID

    返回:
        渲染后的检查记录页面
    """
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限查看该日志的检查记录', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 确保 log.log_date 是 datetime 类型
    if isinstance(log.log_date, str):
        try:
            log_date = datetime.strptime(log.log_date, '%Y-%m-%d %H:%M:%S')
        except ValueError:
            # 如果解析失败，尝试去掉毫秒部分
            log_date = datetime.strptime(log.log_date.split('.')[0], '%Y-%m-%d %H:%M:%S')
    else:
        log_date = log.log_date

    # 获取前一天和后一天的日志
    prev_log = DailyLog.query.filter(
        DailyLog.log_date < log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.desc()).first()

    next_log = DailyLog.query.filter(
        DailyLog.log_date > log_date,
        DailyLog.area_id == user_area.id
    ).order_by(DailyLog.log_date.asc()).first()

    # 获取检查记录
    morning_inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type='morning'
    ).order_by(InspectionRecord.inspection_time).all()

    noon_inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type='noon'
    ).order_by(InspectionRecord.inspection_time).all()

    evening_inspections = InspectionRecord.query.filter_by(
        daily_log_id=log_id,
        inspection_type='evening'
    ).order_by(InspectionRecord.inspection_time).all()

    # 获取每个检查项的照片
    try:
        for inspection_list in [morning_inspections, noon_inspections, evening_inspections]:
            for inspection in inspection_list:
                # 使用原始SQL查询获取照片
                sql = text("""
                SELECT id, file_path, rating, upload_time
                FROM photos
                WHERE reference_type = 'inspection' AND reference_id = :reference_id
                ORDER BY upload_time DESC
                """)

                result = db.session.execute(sql, {'reference_id': inspection.id})

                photos = []
                for row in result:
                    photos.append({
                        'id': row[0],
                        'file_path': row[1],
                        'rating': row[2],
                        'upload_time': row[3]
                    })

                inspection.photos = photos
    except Exception as e:
        # 如果photos表不存在，忽略错误
        current_app.logger.error(f"获取照片失败: {str(e)}")
        pass

    # 获取今天的日期，用于日期导航
    today = date.today()

    # 导入 timedelta，用于日期计算
    from datetime import timedelta

    return render_template('daily_management/simplified_inspection.html',
                          title='检查记录',
                          log=log,
                          prev_log=prev_log,
                          next_log=next_log,
                          morning_inspections=morning_inspections,
                          noon_inspections=noon_inspections,
                          evening_inspections=evening_inspections,
                          today=today,
                          timedelta=timedelta)

# 通过日期访问检查记录
@daily_management_bp.route('/inspections/date/<date_str>', methods=['GET'])
@login_required
def inspections_by_date(date_str):
    """通过日期访问检查记录

    参数:
        date_str: 日期字符串，格式为 YYYY-MM-DD

    返回:
        重定向到对应日期的检查记录页面
    """
    try:
        # 解析日期
        log_date = datetime.strptime(date_str, '%Y-%m-%d').date()

        # 查找对应日期的日志
        log = DailyLog.query.filter_by(log_date=log_date).first()

        # 获取视图类型参数
        view_type = request.args.get('view', 'card')

        # 如果找到日志，重定向到检查记录页面
        if log:
            if view_type == 'table':
                return redirect(url_for('daily_management.inspections_table', log_id=log.id))
            elif view_type == 'card_layout':
                return redirect(url_for('daily_management.inspections_card_layout', log_id=log.id))
            elif view_type == 'simple_table':
                return redirect(url_for('daily_management.inspections_simple_table', log_id=log.id))
            elif view_type == 'category_cards':
                return redirect(url_for('daily_management.inspections_category_cards', log_id=log.id))
            elif view_type == 'simplified':
                return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))
            else:
                # 默认使用简化检查记录页面
                return redirect(url_for('daily_management.simplified_inspection', log_id=log.id))

        # 如果没有找到日志，创建一个新的日志
        sql = text("""
        INSERT INTO daily_logs
        (log_date, area_id, created_by)
        OUTPUT inserted.id
        VALUES
        (CONVERT(DATETIME2(1), :log_date, 120), :area_id, :created_by)
        """)

        # 准备参数
        params = {
            'log_date': log_date.strftime('%Y-%m-%d'),
            'area_id': current_user.area_id,
            'created_by': current_user.id
        }

        # 执行 SQL
        result = db.session.execute(sql, params)
        log_id = result.fetchone()[0]
        db.session.commit()

        # 重定向到新创建的日志的检查记录页面
        if view_type == 'table':
            return redirect(url_for('daily_management.inspections_table', log_id=log_id))
        elif view_type == 'card_layout':
            return redirect(url_for('daily_management.inspections_card_layout', log_id=log_id))
        elif view_type == 'simple_table':
            return redirect(url_for('daily_management.inspections_simple_table', log_id=log_id))
        elif view_type == 'category_cards':
            return redirect(url_for('daily_management.inspections_category_cards', log_id=log_id))
        elif view_type == 'simplified':
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
        else:
            # 默认使用简化检查记录页面
            return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
    except Exception as e:
        flash(f'访问检查记录失败: {str(e)}', 'danger')
        return redirect(url_for('daily_management.index'))

# 添加检查记录
@daily_management_bp.route('/api/inspections/add', methods=['POST'])
@login_required
def add_inspection():
    """添加检查记录API"""
    try:
        # 获取表单数据
        data = request.form.to_dict()

        # 获取日志ID
        log_id = data.get('log_id', type=int)
        if not log_id:
            return jsonify({'success': False, 'message': '缺少日志ID参数'}), 400

        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限添加该日志的检查记录'}), 403

        # 创建检查记录
        inspection = InspectionRecord(
            daily_log_id=log_id,
            inspection_type=data.get('inspection_type'),
            category=data.get('category'),
            item_name=data.get('item_name'),
            status=data.get('status'),
            description=data.get('description'),
            inspector=data.get('inspector') or current_user.real_name or current_user.username,
            inspection_time=datetime.now().replace(microsecond=0),
            created_by=current_user.id
        )

        db.session.add(inspection)
        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'inspection')

            if file_path:
                # 创建照片记录
                photo = Photo(
                    file_path=file_path,
                    reference_type='inspection',
                    reference_id=inspection.id,
                    upload_time=datetime.now().replace(microsecond=0),
                    uploaded_by=current_user.id
                )

                db.session.add(photo)
                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '检查记录添加成功',
            'inspection': {
                'id': inspection.id,
                'inspection_type': inspection.inspection_type,
                'category': inspection.category,
                'item_name': inspection.item_name,
                'status': inspection.status,
                'description': inspection.description,
                'inspector': inspection.inspector,
                'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"添加检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'添加检查记录失败: {str(e)}'}), 500

# 更新检查记录
@daily_management_bp.route('/api/inspections/update/<int:inspection_id>', methods=['POST'])
@login_required
def update_inspection(inspection_id):
    """更新检查记录API"""
    try:
        # 获取检查记录
        inspection = InspectionRecord.query.get_or_404(inspection_id)

        # 获取日志
        log = DailyLog.query.get_or_404(inspection.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限更新该检查记录'}), 403

        # 获取表单数据
        data = request.form.to_dict()

        # 更新检查记录
        inspection.inspection_type = data.get('inspection_type', inspection.inspection_type)
        inspection.category = data.get('category', inspection.category)
        inspection.item_name = data.get('item_name', inspection.item_name)
        inspection.status = data.get('status', inspection.status)
        inspection.description = data.get('description', inspection.description)
        inspection.inspector = data.get('inspector', inspection.inspector)
        inspection.updated_at = datetime.now().replace(microsecond=0)

        db.session.commit()

        # 处理照片上传
        photo_file = request.files.get('photo')
        if photo_file and photo_file.filename:
            file_path = handle_photo_upload(photo_file, 'inspection')

            if file_path:
                # 创建照片记录
                photo = Photo(
                    file_path=file_path,
                    reference_type='inspection',
                    reference_id=inspection.id,
                    upload_time=datetime.now().replace(microsecond=0),
                    uploaded_by=current_user.id
                )

                db.session.add(photo)
                db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '检查记录更新成功',
            'inspection': {
                'id': inspection.id,
                'inspection_type': inspection.inspection_type,
                'category': inspection.category,
                'item_name': inspection.item_name,
                'status': inspection.status,
                'description': inspection.description,
                'inspector': inspection.inspector,
                'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S')
            }
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'更新检查记录失败: {str(e)}'}), 500

# 删除检查记录
@daily_management_bp.route('/api/inspections/delete/<int:inspection_id>', methods=['POST'])
@login_required
def delete_inspection(inspection_id):
    """删除检查记录API"""
    try:
        # 获取检查记录
        inspection = InspectionRecord.query.get_or_404(inspection_id)

        # 获取日志
        log = DailyLog.query.get_or_404(inspection.daily_log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限删除该检查记录'}), 403

        # 删除关联的照片
        photos = Photo.query.filter_by(reference_type='inspection', reference_id=inspection_id).all()
        for photo in photos:
            db.session.delete(photo)

        # 删除检查记录
        db.session.delete(inspection)
        db.session.commit()

        # 返回成功响应
        return jsonify({
            'success': True,
            'message': '检查记录删除成功'
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"删除检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'删除检查记录失败: {str(e)}'}), 500

# 获取检查记录
@daily_management_bp.route('/api/inspections/<int:log_id>', methods=['GET'])
@login_required
def get_inspections(log_id):
    """获取检查记录API"""
    try:
        # 获取日志
        log = DailyLog.query.get_or_404(log_id)

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area or log.area_id != user_area.id:
            return jsonify({'success': False, 'message': '您没有权限查看该日志的检查记录'}), 403

        # 获取检查类型参数
        inspection_type = request.args.get('type')

        # 查询检查记录
        query = InspectionRecord.query.filter_by(daily_log_id=log_id)

        # 如果指定了检查类型，则按类型筛选
        if inspection_type:
            query = query.filter_by(inspection_type=inspection_type)

        # 获取检查记录
        inspections = query.order_by(InspectionRecord.inspection_time).all()

        # 构建响应数据
        result = []
        for inspection in inspections:
            # 获取照片
            photos = Photo.query.filter_by(reference_type='inspection', reference_id=inspection.id).all()
            photo_list = []
            for photo in photos:
                photo_list.append({
                    'id': photo.id,
                    'file_path': photo.file_path,
                    'upload_time': photo.upload_time.strftime('%Y-%m-%d %H:%M:%S') if photo.upload_time else None
                })

            # 添加检查记录
            result.append({
                'id': inspection.id,
                'inspection_type': inspection.inspection_type,
                'category': inspection.category,
                'item_name': inspection.item_name,
                'status': inspection.status,
                'description': inspection.description,
                'inspector': inspection.inspector,
                'inspection_time': inspection.inspection_time.strftime('%Y-%m-%d %H:%M:%S') if inspection.inspection_time else None,
                'photos': photo_list
            })

        # 返回成功响应
        return jsonify({
            'success': True,
            'inspections': result
        })

    except Exception as e:
        current_app.logger.error(f"获取检查记录失败: {str(e)}")
        return jsonify({'success': False, 'message': f'获取检查记录失败: {str(e)}'}), 500

@daily_management_bp.route('/add-inspection-photo/<int:log_id>/<string:type>', methods=['GET', 'POST'])
@login_required
def add_inspection_photo(log_id, type):
    """添加检查照片页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限添加该日志的检查照片', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 创建表单实例用于CSRF保护
    form = FlaskForm()

    if request.method == 'POST':
        try:
            # 处理照片上传
            photo_file = request.files.get('photo')
            if photo_file and photo_file.filename:
                file_path = handle_photo_upload(photo_file, 'inspection')

                if file_path:
                    # 创建照片记录
                    photo = Photo(
                        file_path=file_path,
                        reference_type='inspection',
                        reference_id=log_id,  # 使用日志ID作为引用ID
                        upload_time=datetime.now().replace(microsecond=0),
                        uploaded_by=current_user.id
                    )

                    db.session.add(photo)
                    db.session.commit()
                    flash('照片上传成功', 'success')
                    return redirect(url_for('daily_management.simplified_inspection', log_id=log_id))
                else:
                    flash('照片上传失败', 'danger')
            else:
                flash('请选择要上传的照片', 'warning')
        except Exception as e:
            db.session.rollback()
            current_app.logger.error(f"上传照片失败: {str(e)}")
            flash(f'上传照片失败: {str(e)}', 'danger')

    return render_template('daily_management/add_inspection_photo.html',
                          title='添加检查照片',
                          log=log,
                          type=type,
                          form=form)

@daily_management_bp.route('/generate-photo-upload-qrcode/<int:log_id>', methods=['GET'])
@login_required
def generate_photo_upload_qrcode(log_id):
    """生成照片上传二维码"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限生成该日志的照片上传二维码', 'danger')
        return redirect(url_for('daily_management.logs'))

    # 生成上传照片的URL
    upload_url = url_for('daily_management.public_upload_inspection_photo',
                        school_id=user_area.id,
                        log_id=log_id,
                        inspection_type='morning',
                        _external=True)

    # 生成二维码
    qrcode_base64 = generate_qrcode_base64(upload_url)

    return render_template('daily_management/photo_upload_qrcode.html',
                          title='照片上传二维码',
                          log=log,
                          qrcode_base64=qrcode_base64,
                          upload_url=upload_url)

@daily_management_bp.route('/rate-inspection-photos/<int:log_id>', methods=['GET'])
@login_required
def rate_inspection_photos(log_id):
    """评分检查照片页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限评分该日志的检查照片', 'danger')
        return redirect(url_for('daily_management.logs'))

    return render_template('daily_management/rate_inspection_photos.html',
                          title='评分检查照片',
                          log=log)

@daily_management_bp.route('/print-inspection-photos/<int:log_id>', methods=['GET'])
@login_required
def print_inspection_photos(log_id):
    """打印检查照片页面"""
    # 获取日志
    log = DailyLog.query.get_or_404(log_id)

    # 获取用户所属学校
    user_area = current_user.get_current_area()
    if not user_area or log.area_id != user_area.id:
        flash('您没有权限打印该日志的检查照片', 'danger')
        return redirect(url_for('daily_management.logs'))

    return render_template('daily_management/print_inspection_photos.html',
                          title='打印检查照片',
                          log=log)
